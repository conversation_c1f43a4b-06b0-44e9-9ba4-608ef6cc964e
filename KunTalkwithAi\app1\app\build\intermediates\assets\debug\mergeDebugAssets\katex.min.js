// Simplified KaTeX implementation for basic math rendering
(function(global) {
    'use strict';
    
    var katex = {};
    
    // Basic LaTeX to HTML conversion
    var conversions = {
        // Greek letters
        '\\\\alpha': 'α', '\\\\beta': 'β', '\\\\gamma': 'γ', '\\\\delta': 'δ',
        '\\\\epsilon': 'ε', '\\\\zeta': 'ζ', '\\\\eta': 'η', '\\\\theta': 'θ',
        '\\\\iota': 'ι', '\\\\kappa': 'κ', '\\\\lambda': 'λ', '\\\\mu': 'μ',
        '\\\\nu': 'ν', '\\\\xi': 'ξ', '\\\\pi': 'π', '\\\\rho': 'ρ',
        '\\\\sigma': 'σ', '\\\\tau': 'τ', '\\\\upsilon': 'υ', '\\\\phi': 'φ',
        '\\\\chi': 'χ', '\\\\psi': 'ψ', '\\\\omega': 'ω',
        
        // Capital Greek letters
        '\\\\Alpha': 'Α', '\\\\Beta': 'Β', '\\\\Gamma': 'Γ', '\\\\Delta': 'Δ',
        '\\\\Epsilon': 'Ε', '\\\\Zeta': 'Ζ', '\\\\Eta': 'Η', '\\\\Theta': 'Θ',
        '\\\\Iota': 'Ι', '\\\\Kappa': 'Κ', '\\\\Lambda': 'Λ', '\\\\Mu': 'Μ',
        '\\\\Nu': 'Ν', '\\\\Xi': 'Ξ', '\\\\Pi': 'Π', '\\\\Rho': 'Ρ',
        '\\\\Sigma': 'Σ', '\\\\Tau': 'Τ', '\\\\Upsilon': 'Υ', '\\\\Phi': 'Φ',
        '\\\\Chi': 'Χ', '\\\\Psi': 'Ψ', '\\\\Omega': 'Ω',
        
        // Mathematical operators
        '\\\\pm': '±', '\\\\mp': '∓', '\\\\times': '×', '\\\\div': '÷',
        '\\\\cdot': '·', '\\\\bullet': '•', '\\\\circ': '∘',
        '\\\\leq': '≤', '\\\\geq': '≥', '\\\\neq': '≠', '\\\\approx': '≈',
        '\\\\equiv': '≡', '\\\\sim': '∼', '\\\\simeq': '≃', '\\\\cong': '≅',
        '\\\\propto': '∝', '\\\\parallel': '∥', '\\\\perp': '⊥',
        '\\\\in': '∈', '\\\\notin': '∉', '\\\\subset': '⊂', '\\\\supset': '⊃',
        '\\\\subseteq': '⊆', '\\\\supseteq': '⊇', '\\\\cup': '∪', '\\\\cap': '∩',
        '\\\\emptyset': '∅', '\\\\varnothing': '∅',
        '\\\\infty': '∞', '\\\\partial': '∂', '\\\\nabla': '∇',
        '\\\\forall': '∀', '\\\\exists': '∃', '\\\\nexists': '∄',
        '\\\\therefore': '∴', '\\\\because': '∵',
        '\\\\leftarrow': '←', '\\\\rightarrow': '→', '\\\\leftrightarrow': '↔',
        '\\\\Leftarrow': '⇐', '\\\\Rightarrow': '⇒', '\\\\Leftrightarrow': '⇔',
        '\\\\uparrow': '↑', '\\\\downarrow': '↓', '\\\\updownarrow': '↕',
        
        // Integrals and sums
        '\\\\int': '∫', '\\\\iint': '∬', '\\\\iiint': '∭', '\\\\oint': '∮',
        '\\\\sum': '∑', '\\\\prod': '∏', '\\\\coprod': '∐',
        '\\\\bigcup': '⋃', '\\\\bigcap': '⋂', '\\\\bigsqcup': '⊔',
        '\\\\bigvee': '⋁', '\\\\bigwedge': '⋀', '\\\\bigotimes': '⊗',
        '\\\\bigoplus': '⊕', '\\\\bigodot': '⊙',
        
        // Other symbols
        '\\\\ldots': '…', '\\\\cdots': '⋯', '\\\\vdots': '⋮', '\\\\ddots': '⋱',
        '\\\\angle': '∠', '\\\\triangle': '△', '\\\\square': '□',
        '\\\\diamond': '◊', '\\\\star': '⋆', '\\\\dagger': '†', '\\\\ddagger': '‡',
        '\\\\checkmark': '✓', '\\\\sharp': '♯', '\\\\flat': '♭', '\\\\natural': '♮'
    };
    
    function processSupSub(latex) {
        // Handle superscripts and subscripts
        latex = latex.replace(/\^{([^}]*)}/g, '<sup>$1</sup>');
        latex = latex.replace(/_{([^}]*)}/g, '<sub>$1</sub>');
        latex = latex.replace(/\^([a-zA-Z0-9])/g, '<sup>$1</sup>');
        latex = latex.replace(/_([a-zA-Z0-9])/g, '<sub>$1</sub>');
        return latex;
    }
    
    function processFractions(latex) {
        // Handle fractions
        latex = latex.replace(/\\\\frac{([^}]*)}{([^}]*)}/g, 
            '<span class="katex-frac"><span class="katex-frac-num">$1</span><span class="katex-frac-line"></span><span class="katex-frac-den">$2</span></span>');
        return latex;
    }
    
    function processRoots(latex) {
        // Handle square roots
        latex = latex.replace(/\\\\sqrt{([^}]*)}/g, '√($1)');
        latex = latex.replace(/\\\\sqrt\[([^\]]*)\]{([^}]*)}/g, '<sup>$1</sup>√($2)');
        return latex;
    }
    
    function processBoxed(latex) {
        // Handle boxed expressions
        latex = latex.replace(/\\\\boxed{([^}]*)}/g, '<span class="katex-boxed">$1</span>');
        return latex;
    }
    
    function processTextCommands(latex) {
        // Handle text commands
        latex = latex.replace(/\\\\text{([^}]*)}/g, '<span class="katex-text">$1</span>');
        latex = latex.replace(/\\\\textbf{([^}]*)}/g, '<span class="katex-textbf">$1</span>');
        latex = latex.replace(/\\\\textit{([^}]*)}/g, '<span class="katex-textit">$1</span>');
        return latex;
    }
    
    katex.render = function(latex, element, options) {
        options = options || {};
        
        try {
            var processed = latex;
            
            // Apply symbol conversions
            for (var symbol in conversions) {
                var regex = new RegExp(symbol, 'g');
                processed = processed.replace(regex, conversions[symbol]);
            }
            
            // Process mathematical structures
            processed = processFractions(processed);
            processed = processRoots(processed);
            processed = processSupSub(processed);
            processed = processBoxed(processed);
            processed = processTextCommands(processed);
            
            // Create the rendered content
            var className = 'katex';
            if (options.displayMode) {
                className += ' katex-display';
            }
            
            element.innerHTML = '<span class="' + className + '">' + processed + '</span>';
            
            // Add custom styles for fractions and boxed content
            if (!document.getElementById('katex-custom-styles')) {
                var style = document.createElement('style');
                style.id = 'katex-custom-styles';
                style.textContent = `
                    .katex-frac {
                        display: inline-block;
                        vertical-align: middle;
                        text-align: center;
                    }
                    .katex-frac-num, .katex-frac-den {
                        display: block;
                        line-height: 1;
                    }
                    .katex-frac-line {
                        display: block;
                        height: 1px;
                        background: currentColor;
                        margin: 2px 0;
                    }
                    .katex-boxed {
                        border: 1px solid currentColor;
                        padding: 2px 4px;
                        border-radius: 2px;
                    }
                    .katex-text {
                        font-family: serif;
                        font-style: normal;
                    }
                    .katex-textbf {
                        font-weight: bold;
                    }
                    .katex-textit {
                        font-style: italic;
                    }
                `;
                document.head.appendChild(style);
            }
            
        } catch (error) {
            if (!options.throwOnError) {
                element.innerHTML = '<span style="color: red;">Math render error: ' + error.message + '</span>';
            } else {
                throw error;
            }
        }
    };
    
    // Export katex
    if (typeof module !== 'undefined' && module.exports) {
        module.exports = katex;
    } else {
        global.katex = katex;
    }
    
})(this);