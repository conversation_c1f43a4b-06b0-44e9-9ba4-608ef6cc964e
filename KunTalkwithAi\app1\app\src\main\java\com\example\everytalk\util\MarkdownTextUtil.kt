package com.example.everytalk.util

import androidx.compose.ui.graphics.Color
import java.util.regex.Pattern

// Sealed interface for different types of content blocks
sealed interface ContentBlock {
    data class TextBlock(val content: String) : ContentBlock
    data class MathBlock(val latex: String, val isDisplay: Boolean) : ContentBlock
    data class CodeBlock(val code: String, val language: String?) : ContentBlock
}

fun parseToContentBlocks(markdown: String): List<ContentBlock> {
    // Enhanced regex to find code blocks, block math, and inline math including LaTeX \(...\) and \[...\] formats
    val pattern = Pattern.compile("(?s)(```(.*?)```|\\$\\$(.*?)\\$\\$|\\\\\\[(.*?)\\\\\\]|(?<!\\\\)\\$(.*?)(?<!\\\\)\\$|\\\\\\((.*?)\\\\\\)|\\\\boxed\\{([^}]*)\\})")
    val matcher = pattern.matcher(markdown)
    val blocks = mutableListOf<ContentBlock>()
    var lastIndex = 0

    while (matcher.find()) {
        // Add the text part before the match
        if (matcher.start() > lastIndex) {
            val textContent = markdown.substring(lastIndex, matcher.start())
            // 更宽松的检查，只跳过完全为空的内容
            if (textContent.isNotEmpty()) {
                blocks.add(ContentBlock.TextBlock(textContent))
            }
        }

        // Determine the type of the matched block
        val codeMatch = matcher.group(2)
        val blockMathMatch = matcher.group(3)
        val latexBlockMathMatch = matcher.group(4)  // \[...\] format
        val inlineMathMatch = matcher.group(5)
        val latexInlineMathMatch = matcher.group(6)  // \(...\) format
        val boxedMathMatch = matcher.group(7)  // \boxed{...} format

        when {
            codeMatch != null -> {
                val lines = codeMatch.trim().lines()
                val language = lines.firstOrNull()?.takeIf { it.isNotBlank() }
                val code = if (language != null) lines.drop(1).joinToString("\n") else codeMatch.trim()
                blocks.add(ContentBlock.CodeBlock(code, language))
            }
            blockMathMatch != null -> blocks.add(ContentBlock.MathBlock(blockMathMatch.trim(), isDisplay = true))
            latexBlockMathMatch != null -> blocks.add(ContentBlock.MathBlock(latexBlockMathMatch.trim(), isDisplay = true))
            inlineMathMatch != null -> blocks.add(ContentBlock.MathBlock(inlineMathMatch.trim(), isDisplay = false))
            latexInlineMathMatch != null -> blocks.add(ContentBlock.MathBlock(latexInlineMathMatch.trim(), isDisplay = false))
            boxedMathMatch != null -> blocks.add(ContentBlock.MathBlock("\\boxed{${boxedMathMatch.trim()}}", isDisplay = false))
        }
        lastIndex = matcher.end()
    }

    // Add the remaining text part after the last match
    if (lastIndex < markdown.length) {
        val remainingText = markdown.substring(lastIndex)
        // 更宽松的检查，只跳过完全为空的内容
        if (remainingText.isNotEmpty()) {
            blocks.add(ContentBlock.TextBlock(remainingText))
        }
    }

    return blocks
}