package com.example.everytalk.util

import org.junit.Test
import org.junit.Assert.*

/**
 * 数学公式识别测试
 */
class MathFormulaRecognitionTest {

    /**
     * 测试MessageProcessor的数学公式自动识别功能
     */
    @Test
    fun testMathFormulaRecognition() {
        val processor = MessageProcessor()
        
        // 测试用例1: 勾股定理
        val text1 = "勾股定理，也叫毕达哥拉斯定理：a^2 + b^2 = c^2"
        val result1 = processor.intelligentErrorCorrection(text1)
        assertTrue("应该包含LaTeX数学公式", result1.contains("$") || result1.contains("$$"))
        
        // 测试用例2: 具体数值计算
        val text2 = "如果一个直角三角形的两条直角边分别是 3 和 4：3^2 + 4^2 = 9 + 16 = 25"
        val result2 = processor.intelligentErrorCorrection(text2)
        assertTrue("应该包含LaTeX数学公式", result2.contains("$"))
        
        // 测试用例3: 开方表达式
        val text3 = "斜边就是：\\sqrt{25} = 5"
        val result3 = processor.intelligentErrorCorrection(text3)
        assertTrue("应该包含LaTeX数学公式", result3.contains("$"))
        
        // 测试用例4: 复合数学表达式
        val text4 = "用符号表示就是：\\[ a^2 + b^2 = c^2 \\]"
        val result4 = processor.intelligentErrorCorrection(text4)
        assertTrue("应该保持或添加数学公式标记", result4.contains("$") || result4.contains("\\["))
        
        println("测试结果:")
        println("原文1: $text1")
        println("处理后1: $result1")
        println()
        println("原文2: $text2") 
        println("处理后2: $result2")
        println()
        println("原文3: $text3")
        println("处理后3: $result3")
        println()
        println("原文4: $text4")
        println("处理后4: $result4")
    }
    
    /**
     * 测试parseToContentBlocks函数对处理后文本的解析
     */
    @Test
    fun testContentBlockParsing() {
        // 模拟经过MessageProcessor处理后的文本
        val processedText = "勾股定理：\$a^2 + b^2 = c^2\$，其中 \$c\$ 是斜边。"
        
        val blocks = parseToContentBlocks(processedText)
        
        // 应该包含文本块和数学块
        var hasTextBlock = false
        var hasMathBlock = false
        
        blocks.forEach { block ->
            when (block) {
                is ContentBlock.TextBlock -> hasTextBlock = true
                is ContentBlock.MathBlock -> hasMathBlock = true
                else -> {}
            }
        }
        
        assertTrue("应该包含文本块", hasTextBlock)
        assertTrue("应该包含数学块", hasMathBlock)
        
        println("解析的内容块:")
        blocks.forEachIndexed { index, block ->
            when (block) {
                is ContentBlock.TextBlock -> println("文本块 $index: ${block.content}")
                is ContentBlock.MathBlock -> println("数学块 $index: ${block.latex} (显示模式: ${block.isDisplay})")
                is ContentBlock.CodeBlock -> println("代码块 $index: ${block.code}")
            }
        }
    }
}